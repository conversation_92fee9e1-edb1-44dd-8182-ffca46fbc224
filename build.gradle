plugins {
    id "org.springframework.boot" version "${spring_boot_version}"
    id "com.gorylenko.gradle-git-properties" version "${gradle_git_properties_version}"
    id "org.graalvm.buildtools.native" version "${graalvm_buildtools_native_version}"
    id "org.sonarqube" version "${sonarqube_version}"
    id "org.owasp.dependencycheck" version "${dependencycheck_version}"
}

//sonar {
//    properties {
//        // Set SONAR_TOKEN in System Environment Variable
//        property "sonar.projectKey", "Chid-SToken-Develop"
//        property "sonar.projectName", "Chid-SToken-Develop"
//        property "sonar.dependencyCheck.htmlReportPath", "chid-endpoint-stoken/build/reports/dependency-check-report.html"
//    }
//}

//afterEvaluate {
//    tasks.named("sonar").configure {
//        // Define a list of subprojects that should run dependencyCheckAnalyze
//        def projectsToCheck = [':hammer-loyalty-endpoint-core']
//        projectsToCheck.each { subprojectName ->
//            def subproject = project(subprojectName)
//            def checkTask = subproject.tasks.findByName("dependencyCheckAnalyze")
//            if (checkTask != null) {
//                dependsOn(checkTask)
//            }
//        }
//    }
//}

//sub projects setting
subprojects {
    apply from: "${parent.projectDir.canonicalPath}/version.gradle"
    apply from: "${parent.projectDir.canonicalPath}/commonbuild.gradle"
    apply plugin: "org.sonarqube"
    apply plugin: "org.owasp.dependencycheck"

    group = 'com.neurogine.hammer.loyalty'

    dependencyCheck {
        nvd.apiKey = System.getenv('DEPENDENCY_CHECK_NVD_API_KEY')
        formats=["HTML","XML"]
        data.directory = "/nvd/11.0" // standardize to 1 folder, to simplify Jenkins jobs
        suppressionFile = "http://192.168.88.76/dependencycheck/suppression/-/raw/main/prometheus-metrics-suppression.xml"
    }

    dependencies {
        // Apply Spring Boot BOM for dependency management
        implementation platform("org.springframework.boot:spring-boot-dependencies:${spring_boot_version}")
    }
}

