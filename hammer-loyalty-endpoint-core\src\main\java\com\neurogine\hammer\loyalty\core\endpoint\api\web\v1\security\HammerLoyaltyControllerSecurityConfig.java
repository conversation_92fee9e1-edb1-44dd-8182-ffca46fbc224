package com.neurogine.hammer.loyalty.core.endpoint.api.web.v1.security;

import com.neurogine.juno.integration.core.web.config.JunoWebProperties;
import com.neurogine.juno.integration.core.web.security.JunoHttpSecurityUtil;
import com.neurogine.juno.integration.user.security.UserRoleSecurity;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

import static com.neurogine.juno.integration.user.util.SecurityFilterChainUtil.hasRolePermission;

/**
 * <AUTHOR> <PERSON>g
 **/
@Configuration(proxyBeanMethods = false)
public class HammerLoyaltyControllerSecurityConfig {

    private static final String LOYALTY_PERMISSION = "v1.loyalty";

    @Bean
    @Order(JunoWebProperties.DEFAULT_SECURITY_FILTER_CHAIN_ORDER)
    public SecurityFilterChain loyaltyFilterChain(HttpSecurity http,
                                                  UserRoleSecurity userRoleSecurity) throws Exception {
        http
                .securityMatcher("/loyalty/**")
                .authorizeHttpRequests(authorize -> authorize
//                        .requestMatchers(HttpMethod.POST,
//                                "/loyalty/admin/", "/loyalty/verify")
//                        .access(hasRolePermission(userRoleSecurity, LOYALTY_PERMISSION))
                        .anyRequest().denyAll()
                );
        JunoHttpSecurityUtil.configureJunoDefaultHttpSecurity(http);
        return http.build();
    }
}
