package com.neurogine.hammer.loyalty.core.endpoint.api.web.v1.security;

import com.neurogine.juno.integration.core.web.config.JunoWebProperties;
import com.neurogine.juno.integration.core.web.security.JunoHttpSecurityUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

/**
 * <AUTHOR> Leong
 **/
@Configuration(proxyBeanMethods = false)
public class HammerLoyaltySwaggerSecurityConfig {

    @Bean
    @Order(JunoWebProperties.DEFAULT_SECURITY_FILTER_CHAIN_ORDER)
    public SecurityFilterChain licenseSwaggerFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatcher("/swaggerlicense/**")
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/swaggerlicense/**").permitAll()
                        .anyRequest().denyAll()
                );
        JunoHttpSecurityUtil.configureJunoDefaultHttpSecurity(http);
        return http.build();
    }
}
