
juno.data.mongodb.databaseName.core=hammer
juno.data.mongodb.databaseName.history=hammerHistory
juno.data.mongodb.databaseName.audit=hammerAudit

#juno.user.email.login-host=https://nlicenseuatmanage.neuroginedev.com/
#juno.user.email.reset-password-host=https://nlicenseuatmanage.neuroginedev.com/
#
#juno.user.email.reset-password-path=#/auth/resetPasswordFromEmail/
#juno.user.email.loginPath=#/auth/login


juno.message-source.notification-basenames=locale.notificationExt,locale.notifications


spring.mail.username=<EMAIL>
spring.mail.password=ghfzbrbkxouidtyb

juno.user.access-key.max-key-per-user=1000

# Default swagger ui path for this endpoint
springdoc.api-docs.path=/swaggerlicense/v3/api-docs
springdoc.swagger-ui.path=/swaggerlicense/swagger-ui.html

#juno.user.user-role.additional-policy-files.License=\
#  License_Admin_Policy.json,\
#  License_Client_Policy.json
#
#juno.user.user-role.additional-policy-group-files.License=\
#  License_Admin_Policy_Group.json,\
#  License_Client_Policy_Group.json
#
#juno.user.user-role.additional-relation-files.LicenseRoles=\
#  License_Admin_relation.json,\
#  License_Client_relation.json
#
#juno.user.user-role.additional-role-files.LicenseRoles=\
#  License_Admin_role.json,\
#  License_Client_role.json
#
## TODO for tesing only, user should create from UI
#juno.user.init-system-integration-users.users[0].name=Demo License Client
#juno.user.init-system-integration-users.users[0].user-role=ROLE_CLIENT
#juno.user.init-system-integration-users.users[0].auto-generate-access-key=false
#
## Default Riau Bank User
#juno.user.init-system-integration-users.users[1].name=Riau Bank License Client
#juno.user.init-system-integration-users.users[1].user-role=ROLE_CLIENT
#juno.user.init-system-integration-users.users[1].auto-generate-access-key=false
#
## Default CHID development env user
#juno.user.init-system-integration-users.users[2].name=CHID Dev Env License Client
#juno.user.init-system-integration-users.users[2].user-role=ROLE_CLIENT
#juno.user.init-system-integration-users.users[2].auto-generate-access-key=false
#
#
#
#
