package com.neurogine.hammer.loyalty.data.mongodb;

import org.junit.jupiter.api.Test;
import com.neurogine.hammer.loyalty.data.mongodb.config.HammerLoyaltyCoreMongoAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

@SpringBootTest
@ContextConfiguration(classes = {HammerLoyaltyCoreMongoAutoConfiguration.class})
class HammerLoyaltyDataMongodbCoreApplicationTests {

    @Test
    void contextLoads() {
    }

}
