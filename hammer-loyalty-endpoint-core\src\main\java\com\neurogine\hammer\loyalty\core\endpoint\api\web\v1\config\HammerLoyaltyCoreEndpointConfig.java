package com.neurogine.hammer.loyalty.core.endpoint.api.web.v1.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = {
        MongoAutoConfiguration.class,
        MongoDataAutoConfiguration.class
})
@EnableScheduling // this need to in @SpringBootApplication class
public class HammerLoyaltyCoreEndpointConfig {
    public static void main(String[] args) {
        SpringApplication.run(HammerLoyaltyCoreEndpointConfig.class, args);
    }
}
