package com.neurogine.hammer.loyalty.core.endpoint.api.web.v1.config;

import com.neurogine.hammer.loyalty.core.endpoint.api.web.v1.security.HammerLoyaltySwaggerSecurityConfig;
import com.neurogine.juno.integration.boot.config.CoreConfig;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.Ordered;

import static org.springdoc.core.utils.Constants.SPRINGDOC_ENABLED;

/**
 * <AUTHOR> Leong
 **/
@AutoConfiguration(after = {TaskExecutionAutoConfiguration.class, TaskSchedulingAutoConfiguration.class})
@AutoConfigureOrder(Ordered.HIGHEST_PRECEDENCE)
@Import({CoreConfig.class})
@EnableConfigurationProperties({HammerLoyaltyCoreEndpointProperties.class})
//@ComponentScan(basePackageClasses = {})
public class HammerLoyaltyCoreEndpointAutoConfiguration {
    private HammerLoyaltyCoreEndpointAutoConfiguration() {
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(name = SPRINGDOC_ENABLED, havingValue = "true", matchIfMissing = true)
    @Import({HammerLoyaltyCoreEndpointSwaggerConfig.class, HammerLoyaltySwaggerSecurityConfig.class})
    public static class HammerLoyaltyCoreApiSwaggerConfiguration {
    }

}
