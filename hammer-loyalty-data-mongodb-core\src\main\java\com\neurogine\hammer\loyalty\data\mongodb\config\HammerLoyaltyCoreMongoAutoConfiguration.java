package com.neurogine.hammer.loyalty.data.mongodb.config;

import com.neurogine.juno.data.commons.config.JunoDataApiCoreAutoConfiguration;
import com.neurogine.juno.data.mongodb.config.JunoMongoAutoConfiguration;
import com.neurogine.juno.data.mongodb.config.JunoMongoUserAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * <AUTHOR> Leong
 **/
@AutoConfigureOrder(Ordered.HIGHEST_PRECEDENCE)
@AutoConfiguration(after = {TaskSchedulingAutoConfiguration.class, JunoDataApiCoreAutoConfiguration.class},
        before = {JunoMongoAutoConfiguration.class, JunoMongoUserAutoConfiguration.class})
@EnableConfigurationProperties(HammerLoyaltyCoreMongoProperties.class)
public class HammerLoyaltyCoreMongoAutoConfiguration {
    private HammerLoyaltyCoreMongoAutoConfiguration() {
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(name = HammerLoyaltyCoreMongoProperties.HAMMER_LOYALTY_CORE_REPOSITORIES_ENABLED, havingValue = "true", matchIfMissing = true)
    @ComponentScan()
    public static class HammerLoyaltyCoreMongoRepositoryConfiguration {
    }


}
