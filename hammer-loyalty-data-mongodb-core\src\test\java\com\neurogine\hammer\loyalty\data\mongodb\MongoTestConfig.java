package com.neurogine.hammer.loyalty.data.mongodb;

import com.neurogine.hammer.loyalty.data.mongodb.config.HammerLoyaltyCoreMongoAutoConfiguration;
import com.neurogine.juno.data.commons.config.JunoDataApiCoreAutoConfiguration;
import com.neurogine.juno.data.commons.config.JunoDataApiUserAutoConfiguration;
import com.neurogine.juno.data.mongodb.config.JunoMongoAutoConfiguration;
import com.neurogine.juno.data.mongodb.config.JunoMongoUserAutoConfiguration;
import jakarta.validation.Validator;
import org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

@Configuration(proxyBeanMethods = false)
@ComponentScan(basePackageClasses = {
        JunoDataApiCoreAutoConfiguration.class,
        JunoDataApiUserAutoConfiguration.class,
        JunoMongoUserAutoConfiguration.class,
        JunoMongoAutoConfiguration.class,
        HammerLoyaltyCoreMongoAutoConfiguration.class})
@Import(TaskSchedulingAutoConfiguration.class)
@EnableScheduling
public class MongoTestConfig {

    @Bean
    public Validator validator() {
        return new LocalValidatorFactoryBean();
    }


}
