package com.neurogine.hammer.loyalty.data.mongodb.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "hammer.loyalty.core.data.mongodb")
public class HammerLoyaltyCoreMongoProperties {
    public static final String HAMMER_LOYALTY_CORE_REPOSITORIES_ENABLED = "hammer.loyalty.core.data.mongodb.repositories.enabled";

//    record Repositories(boolean enabled) {
//    }
}
