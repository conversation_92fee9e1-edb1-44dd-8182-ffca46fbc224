package com.neurogine.hammer.loyalty.core.endpoint.api.web.v1.config;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Leong
 */
@ConfigurationProperties(prefix = "license.endpoint.core")
public class HammerLoyaltyCoreEndpointProperties {
    private final Swagger swagger = new Swagger();

    public Swagger getSwagger() {
        return swagger;
    }

    public static class Swagger {
        /**
         * Endpoint Group Name
         */
        private String groupName = "Hammer Loyalty Endpoint";
        /**
         * Paths to match
         */
        private List<String> pathsToMatch = List.of(
                "/loyalty/**");
        /**
         * Paths to exclude
         */
        private List<String> pathsToExclude = new ArrayList<>();

        public String getGroupName() {
            return groupName;
        }

        public void setGroupName(String groupName) {
            this.groupName = groupName;
        }

        public List<String> getPathsToMatch() {
            return pathsToMatch;
        }

        public void setPathsToMatch(List<String> pathsToMatch) {
            this.pathsToMatch = pathsToMatch;
        }

        public List<String> getPathsToExclude() {
            return pathsToExclude;
        }

        public void setPathsToExclude(List<String> pathsToExclude) {
            this.pathsToExclude = pathsToExclude;
        }
    }
}
