dependencies {

    implementation("com.neurogine.juno.core:juno-common-util:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-data-api-core:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-data-api-user:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-data-mongodb-core:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-data-mongodb-user:$juno_core_version")

    implementation("org.apache.commons:commons-lang3")
    implementation("org.springframework.boot:spring-boot-starter-data-mongodb")
    implementation("org.springframework.boot:spring-boot-starter-validation")

    // spring boot auto configure if any
    implementation("org.springframework.boot:spring-boot-autoconfigure")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor:$spring_boot_version")

    // test using embedded mongodb
    testImplementation("com.neurogine.juno.core:juno-data-mongodb-test:$juno_core_version")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}
