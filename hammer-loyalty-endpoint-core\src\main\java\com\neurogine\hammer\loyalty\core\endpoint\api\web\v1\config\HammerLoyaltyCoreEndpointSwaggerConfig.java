package com.neurogine.hammer.loyalty.core.endpoint.api.web.v1.config;


import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
@Configuration(proxyBeanMethods = false)
public class HammerLoyaltyCoreEndpointSwaggerConfig {

    @Bean
    public GroupedOpenApi authEndpointOpenApi(
            HammerLoyaltyCoreEndpointProperties hammerLoyaltyCoreEndpointProperties,
            @Qualifier("propertiesResolverForSchema")
            OpenApiCustomizer propertiesResolverForSchema) {
        HammerLoyaltyCoreEndpointProperties.Swagger swagger = hammerLoyaltyCoreEndpointProperties.getSwagger();
        return GroupedOpenApi.builder()
                .group(swagger.getGroupName())
                .pathsToMatch(swagger.getPathsToMatch().toArray(new String[0]))
                .pathsToExclude(swagger.getPathsToExclude().toArray(new String[0]))
                .addOpenApiCustomizer(propertiesResolverForSchema)
                .build();
    }
}
