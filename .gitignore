.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### IntelliJ IDEA ###
.idea/
.idea/modules.xml
.idea/jarRepositories.xml
.idea/compiler.xml
.idea/libraries/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store
/docker-compose/geth/node1/geth/
/docker-compose/geth/node2/geth/
/docker-compose/geth/node3/geth/
/docker-compose/geth/node1/boot.key
/docker-compose/geth/node2/boot.key
/docker-compose/geth/node3/boot.key

node_modules
.env

# Hardhat files
/cache
/artifacts

# TypeChain files
/typechain
/typechain-types

# solidity-coverage files
/coverage
/coverage.json
/ntoken-besu/besu-compose/QBFT-Network/node1/data/
/ntoken-besu/besu-compose/QBFT-Network/node2/data/
/ntoken-besu/besu-compose/QBFT-Network/node3/data/
/ntoken-besu/besu-compose/QBFT-Network/node4/data/
/ntoken-besu/besu-compose/QBFT-Network/networkFiles/
/ntoken-besu/besu-compose/QBFT-Network/node1/tessera/mysql/
/ntoken-besu/quorum-dev-quickstart/
/ntoken-hyperledger/ca-operation-guide-sample/.fabric-ca-client/
/ntoken-hyperledger/ca-operation-guide-sample/find.sh
/ntoken-hyperledger/organizations/

# Exclude all logs
logs/

### logs ###
*.log
