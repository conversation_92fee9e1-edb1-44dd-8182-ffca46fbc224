plugins {
    id "org.springframework.boot" version "${spring_boot_version}"
    id "com.gorylenko.gradle-git-properties" version "${gradle_git_properties_version}"
    id "org.graalvm.buildtools.native" version "${graalvm_buildtools_native_version}"
}

tasks.named("bootBuildImage") {

    def dockerUsername = System.getenv().dockerUsername
    def dockerPassword = System.getenv().dockerPassword
    if (!dockerUsername) {
        dockerUsername = "-"
    }
    if (!dockerPassword) {
        dockerPassword = "-"
    }
    // delay set dockerUsername and dockerPassword in closure
    docker {
        publishRegistry {
            username = dockerUsername
            password = dockerPassword
            url = "https://dockerregistry.neuroginedev.com:8443/v2/"
            email = "<EMAIL>"
        }
    }
    imageName = "dockerregistry.neuroginedev.com:8443/hammer-loyalty/${project.name}:${version}"
    environment = [
            "BP_JVM_VERSION": "21",
            "BP_NATIVE_IMAGE_BUILD_ARGUMENTS": "-J-Xmx16g -J-Xms8g"
    ]
}

springBoot {
    buildInfo()
}

dependencies {

    implementation(project(":hammer-loyalty-data-mongodb-core"))

    implementation("com.neurogine.juno.core:juno-service-support-jvm:$juno_core_version")

    implementation("com.neurogine.juno.core:juno-common-util:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-integration-core:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-integration-core-config:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-integration-core-security:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-integration-core-web:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-integration-user:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-integration-swagger:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-data-starter-mongodb:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-data-api-core:$juno_core_version")
    implementation("com.neurogine.juno.core:juno-data-api-user:$juno_core_version")
    implementation 'org.springframework.boot:spring-boot-starter-mail'

    //for testing purpose
    implementation ("com.neurogine.juno.core:juno-api-endpoint-core:$juno_core_version")
//    implementation ("com.neurogine.juno.core:juno-api-endpoint-user:$juno_core_version")
//



    implementation("org.apache.commons:commons-lang3")
    implementation("org.springframework:spring-messaging")
    implementation("org.springframework.boot:spring-boot-starter-data-mongodb")
    implementation("org.springframework.boot:spring-boot-actuator-autoconfigure")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-security")

    // swagger
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:$springdoc_version")
    implementation("io.swagger.core.v3:swagger-annotations:$io_swagger_v3_version")

    // spring boot auto configure if any
    implementation("org.springframework.boot:spring-boot-autoconfigure")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor:$spring_boot_version")


    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("org.assertj:assertj-core")
}
