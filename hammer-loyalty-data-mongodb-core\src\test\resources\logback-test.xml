<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <Pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </encoder>
    </appender>

    <logger name="org.springframework.integration" level="info"/>

    <logger name="org.springframework" level="info"/>

    <logger name="com.neurogine" level="debug"/>

    <logger name="org.springframework.data.mongodb.repository.query.StringBasedMongoQuery" level="debug"/>

    <logger name="org.springframework.data.mongodb.core.MongoTemplate" level="debug"/>

    <logger name="org.mongodb" level="debug"/>

    <logger name="org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener"
            level="debug"/>

    <root level="debug">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
