<?xml version="1.0"?>
<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN" "https://checkstyle.org/dtds/configuration_1_3.dtd">
<module name="com.puppycrawl.tools.checkstyle.Checker">

    <!-- Suppressions -->
    <module name="SuppressionFilter">
        <property name="file" value="${config_loc}/checkstyle-suppressions.xml"/>
    </module>

    <!-- Root Checks -->
    <module name="com.puppycrawl.tools.checkstyle.checks.NewlineAtEndOfFileCheck"/>

    <!-- TreeWalker Checks -->
    <module name="com.puppycrawl.tools.checkstyle.TreeWalker">
        <!-- Annotations -->
        <module name="com.puppycrawl.tools.checkstyle.checks.annotation.MissingOverrideCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.annotation.PackageAnnotationCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.annotation.AnnotationLocationCheck">
            <property name="allowSamelineSingleParameterlessAnnotation" value="false"/>
        </module>

        <!-- Block Checks -->
        <module name="com.puppycrawl.tools.checkstyle.checks.blocks.EmptyBlockCheck">
            <property name="option" value="text"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.blocks.LeftCurlyCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.blocks.RightCurlyCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.blocks.AvoidNestedBlocksCheck"/>

        <!-- Class Design -->
        <module name="com.puppycrawl.tools.checkstyle.checks.design.InterfaceIsTypeCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.design.MutableExceptionCheck">
            <property name="format" value="^.*Exception$"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.design.InnerTypeLastCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.design.OneTopLevelClassCheck"/>

        <!-- Coding -->
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.CovariantEqualsCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.EmptyStatementCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.EqualsHashCodeCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.SimplifyBooleanExpressionCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.SimplifyBooleanReturnCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.StringLiteralEqualityCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.NestedForDepthCheck">
            <property name="max" value="3"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.NestedIfDepthCheck">
            <property name="max" value="5"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.NestedTryDepthCheck">
            <property name="max" value="3"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.coding.MultipleVariableDeclarationsCheck"/>

        <module name="com.puppycrawl.tools.checkstyle.checks.coding.OneStatementPerLineCheck"/>

        <!-- Imports -->
        <module name="com.puppycrawl.tools.checkstyle.checks.imports.RedundantImportCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.imports.UnusedImportsCheck">
            <property name="processJavadoc" value="true"/>
        </module>

        <!-- Modifiers -->
        <module name="com.puppycrawl.tools.checkstyle.checks.modifier.ModifierOrderCheck"/>

        <!-- Whitespace -->
        <module name="com.puppycrawl.tools.checkstyle.checks.whitespace.SingleSpaceSeparatorCheck"/>

        <!-- Miscellaneous -->
        <module name="com.puppycrawl.tools.checkstyle.checks.indentation.CommentsIndentationCheck">
            <property name="tokens" value="BLOCK_COMMENT_BEGIN"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.UpperEllCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.ArrayTypeStyleCheck"/>
        <module name="com.puppycrawl.tools.checkstyle.checks.OuterTypeFilenameCheck"/>
        <module name="UnnecessarySemicolonInEnumeration"/>

        <!-- Regexp -->
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpCheck">
            <property name="format" value="[ \t]+$"/>
            <property name="illegalPattern" value="true"/>
            <property name="message" value="Trailing whitespace"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="primitiveClassLiterals"/>
            <property name="maximum" value="0"/>
            <property name="format" value="(Boolean|Character|Byte|Short|Integer|Long|Float|Double|Void)\.TYPE"/>
            <property name="message" value="Please use class literals for primitives and void -- for example, int.class instead of Integer.TYPE."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="toLowerCaseWithoutLocale"/>
            <property name="format" value="\.toLowerCase\(\)"/>
            <property name="maximum" value="0"/>
            <property name="message"
                      value="String.toLowerCase() should be String.toLowerCase(Locale.ROOT)"/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="toUpperCaseWithoutLocale"/>
            <property name="format" value="\.toUpperCase\(\)"/>
            <property name="maximum" value="0"/>
            <property name="message"
                      value="String.toUpperCase() should be String.toUpperCase(Locale.ROOT)"/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="synchronizedKeyword"/>
            <property name="format" value="\bsynchronized\b"/>
            <property name="maximum" value="0"/>
            <property name="message"
                      value="Avoid using the 'synchronized' keyword."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="requireNonNullCheck"/>
            <property name="format" value="\bObjects\.requireNonNull\b"/>
            <property name="maximum" value="0"/>
            <property name="message" value="Avoid using 'Objects.requireNonNull' due to potential NPE risks."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="requireNonNullElseCheck"/>
            <property name="format" value="\bObjects\.requireNonNullElse\b"/>
            <property name="maximum" value="0"/>
            <property name="message" value="Avoid using 'Objects.requireNonNullElse' due to potential NPE risks."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="requireNonNullElseGetCheck"/>
            <property name="format" value="\bObjects\.requireNonNullElseGet\b"/>
            <property name="maximum" value="0"/>
            <property name="message" value="Avoid using 'Objects.requireNonNullElseGet' due to potential NPE risks."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.imports.IllegalImportCheck">
            <property name="id" value="illegalPostConstructCheck"/>
            <property name="regexp" value="true"/>
            <property name="illegalClasses"
                      value="javax.annotation.PostConstruct, jakarta.annotation.PostConstruct"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="maximum" value="0"/>
            <property name="format"
                      value="assertThatExceptionOfType\((NullPointerException|IllegalArgumentException|IOException|IllegalStateException)\.class\)"/>
            <property name="message"
                      value="Please use specialized AssertJ assertThat*Exception method."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="expectedExceptionAnnotation"/>
            <property name="maximum" value="0"/>
            <property name="format" value="\@Test\(expected"/>
            <property name="message" value="Please use AssertJ assertions."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="junit4Assertions"/>
            <property name="maximum" value="0"/>
            <property name="format" value="org\.junit\.Assert"/>
            <property name="message" value="Please use AssertJ assertions."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="junitJupiterAssertions"/>
            <property name="maximum" value="0"/>
            <property name="format" value="org\.junit\.jupiter\.api\.Assertions"/>
            <property name="message" value="Please use AssertJ assertions."/>
            <property name="ignoreComments" value="true"/>
        </module>
        <module name="com.puppycrawl.tools.checkstyle.checks.regexp.RegexpSinglelineJavaCheck">
            <property name="id" value="testNGAssertions"/>
            <property name="maximum" value="0"/>
            <!-- should cover org.testng.Assert and org.testng.AssertJUnit -->
            <property name="format" value="org\.testng\.Assert(JUnit)?"/>
            <property name="message" value="Please use AssertJ assertions."/>
            <property name="ignoreComments" value="true"/>
        </module>

    </module>

</module>
